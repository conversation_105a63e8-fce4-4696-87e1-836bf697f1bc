.allVideosContainer {
    background-color: #fff;
    height: 100vh;
  }
  
  .customHeader {
    background-color: #0d1525;
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .pageTitle {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
  }
  
  .pageSubtitle {
    margin: 5px 0;
    font-size: 16px;
    color: #ccc;
  }
  
  .totalCount {
    font-size: 14px;
    color: #ccc;
  }
  
  .searchSection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
  }
  
  .stats {
    font-size: 14px;
    color: #888787;
  }
  
  .searchWrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 250px;
  }
  
  .searchIcon {
    position: absolute;
    left: 10px;
    color: #888;
    pointer-events: none;
    font-size: 14px;
  }
  
  .clearIcon {
    position: absolute;
    right: 10px;
    color: #888;
    cursor: pointer;
    font-size: 14px;
    transition: color 0.2s ease;
  }
  
  .clearIcon:hover {
    color: #000;
  }
  
  .searchInput {
    width: 100%;
    padding: 8px 30px 8px 32px; // right padding for X icon, left for search icon
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #ccc;
  }
  
  .backButton {
    background-color: #fcd32a;
    color: #000;
    border: none;
    padding: 10px 20px;
    font-weight: bold;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .backButton:hover {
    background-color: #ffd900;
  }
  
  .videoGrid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
    a {
      text-decoration: none;
      color: #000;
      .card {
        border: 1px solid #c9c9c9;
        border-radius: 8px;
        box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.1);
        box-sizing: border-box;
        overflow: hidden;
        padding: 10px;
        transition: transform 0.3s ease;
        .title {
          font-size: 14px;
          font-weight: 600;
          padding: 10px 0;
          height: 40px;
          text-align: center;
        }
        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
  
  .previewImage {
    width: 100%;
    height: auto;
    object-fit: contain; // Ensures the image is not stretched
    max-height: 130px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
  }
  
  .noResults {
    text-align: center;
    font-size: 16px;
    color: #666;
    margin-top: 20px;
    width: 100%;
  }
  
  .loadMoreContainer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .loadMoreButton {
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 6px;
    border: 1px solid #ccc;
    background-color: #f5f5f5;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .loadMoreButton:hover {
    background-color: #ddd;
  }
  
  .backToTopContainer {
    text-align: center;
    margin-top: 20px;
  }
  
  .backToTopButton {
    background-color: #ffd700;
    border: none;
    padding: 10px 20px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.3s ease;
  
    &:hover {
      background-color: #000;
      color: #ffd700;
    }
  }
  
  /* AllVideosView.module.scss */
  .allVideosContainer {
    overflow-anchor: none;
  }
  
  
  // Responsive Styles Begin Here
  @media screen and (max-width: 1260px) {
    .videoCarousel {
      .carouselContainer {
        .carousel {
          grid-template-columns: auto;
        }
      }
    }
    .videoGrid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  // /*Tablet*/
  // @media (max-width: 992px) {
  //   .card {
  //     width: 48%; /* 2 per row */
  //   }
  // }
  
  /*767 Pixel*/
  @media screen and (max-width: 767px) {
    .carouselHeader {
      flex-direction: column;
      gap: 10px;
    }
    .videoGrid {
      grid-template-columns: auto;
    }
  }
  
  /*Smaller Screen*/
  @media (max-width: 600px) {
    .card {
      width: 100%; /* 1 per row */
    }
    .carouselHeader {
      flex-direction: column;
    }
  }
  