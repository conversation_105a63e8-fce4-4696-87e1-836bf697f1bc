import * as React from "react";
import styles from "../components/AllVideosView.module.scss";
import { getStrings } from "../../../common/localeHelper";
// import PlatonLogo from "../assets/PLATON_Logo.svg";

interface DocumentItem {
  Title: string;
  Videos: string;
  PreviewUrl: string;
}

interface AllVideosViewProps {
  title: string;
  docData: DocumentItem[];
  onBack: () => void;
}

const strings = getStrings();
const BATCH_SIZE = 20;

const AllVideosView: React.FC<AllVideosViewProps> = ({
  title,
  docData,
  onBack,
}) => {
  const [searchText, setSearchText] = React.useState<string>("");
  const [visibleCount, setVisibleCount] = React.useState<number>(BATCH_SIZE);

  React.useLayoutEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: "auto" });
  }, []);

  const filteredVideos = docData.filter((item) =>
    item.Title.toLowerCase().includes(searchText.toLowerCase())
  );

  const videosToShow = filteredVideos.slice(0, visibleCount);

  const loadMore = (): void => {
    setVisibleCount((prev) => prev + BATCH_SIZE);
  };

  return (
    <div className={styles.allVideosContainer}>
      {/* Header Section */}
      <div className={styles.customHeader}>
        <div>
          <h2 className={styles.pageTitle}>{title}</h2>
          <p className={styles.pageSubtitle}>{strings.Common.AllVideoText}</p>
          <div className={styles.totalCount}>
            ▶️ {docData.length} {strings.Common.TotalVideo}
          </div>
        </div>
        <button className={styles.backButton} onClick={onBack}>
          {strings.Common.Back}
        </button>
      </div>

      {/* Search Section */}
      <div>
        <div className={styles.searchSection}>
          <div className={styles.stats}>
            Showing {Math.min(visibleCount, filteredVideos.length)} of{" "}
            {filteredVideos.length} records
          </div>
          <div className={styles.searchWrapper}>
            <span className={styles.searchIcon}>
              <i className="fas fa-search" />
            </span>
            <input
              type="text"
              placeholder="Search videos..."
              value={searchText}
              onChange={(e) => {
                setSearchText(e.target.value);
              }}
              className={styles.searchInput}
            />
            {searchText && (
              <span
                className={styles.clearIcon}
                onClick={() => setSearchText("")}
                title="Clear"
              >
                <i className="fas fa-times" />
              </span>
            )}
          </div>
        </div>
        <hr />
      </div>

      {/* Video Grid */}
      <div className={styles.videoGrid}>
        {videosToShow.length > 0 ? (
          videosToShow.map((item) => (
            <a
              key={item.Videos}
              href={`${item.Videos}?web=1`}
              target="_blank"
              rel="noopener noreferrer"
            >
              <div className={styles.card} key={item.Videos}>
                {item.Videos.endsWith(".mp4") ||
                item.Videos.endsWith(".mov") ? (
                  <video controls width="100%" className={styles.previewImage} >
                    <source src={item.Videos} type="video/mp4" />
                  </video>
                ) : item.Videos.endsWith(".wmv") ? (
                  <video
                    controls
                    width="100%"
                    className={styles.previewImage}
                    // poster={PlatonLogo}
                  >
                    <source src={item.Videos} type="video/mp4" />
                  </video>
                ) : (
                  <img
                    src={item.PreviewUrl}
                    alt={`Preview of ${item.Title}`}
                    className={styles.previewImage}
                  />
                )}

                <div className={styles.title}>{item.Title}</div>
              </div>
            </a>
          ))
        ) : (
          <div className={styles.noResults}>{strings.Common.NoRecordText}</div>
        )}
      </div>

      {/* Load More Button */}
      {visibleCount < filteredVideos.length && (
        <div className={styles.loadMoreContainer}>
          <button className={styles.loadMoreButton} onClick={loadMore}>
            {strings.Common.LoadMore}
          </button>
        </div>
      )}
    </div>
  );
};

export default AllVideosView;
