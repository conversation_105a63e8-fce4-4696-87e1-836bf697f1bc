import { useState, useEffect } from "react";
import { getStrings, getCurrentLang, LocaleStrings } from "./localeHelper";

export function useLocale(): { lang: string; strings: LocaleStrings } {
  const [lang, setLang] = useState(getCurrentLang());
  const [strings, setStrings] = useState(getStrings(lang));

  useEffect(() => {
    const interval = setInterval(() => {
      const currentLang = getCurrentLang();
      if (currentLang !== lang) {
        setLang(currentLang);
        setStrings(getStrings(currentLang));
      }
    }, 500); // polling every 500ms

    return () => clearInterval(interval);
  }, [lang]);

  return {lang,strings};
}
