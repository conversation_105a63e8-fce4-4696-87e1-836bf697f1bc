// KeyUserInfo.tsx
import * as React from "react";
import { useState } from "react";
import styles from "./KeyUserInfo.module.scss";
import { IKeyUserInfoProps } from "./IKeyUserInfoProps";
import VideoCarousel from "./InDepthSession";
import ModuleSeries from "./ModuleSeries";
import PlatonDemo from "./PlatonDemo";
import ModuleShorts from "./ModulesShorts";
import TestCases from "./TestCases";
import VideoShortsCarousel from "./VideoShortsCarousel";
import { useLocale } from "../../../common/useLocale";
import AllVideosView from "./AllVideosView";

interface DocumentItem {
  Title: string;
  Videos: string;
  PreviewUrl: string;
}
interface VideoItem {
  Title: string;
  Videos: string;
  PreviewUrl?: string;
  title?: string;
  videos?: string;
  previewUrl?: string;
  url?: string;
  SubTitle?: string;
  subTitle?: string;
  Thumbnail?: string;
}

const VIEW_PARAM = "view";
const STORAGE_KEY = "KeyUserInfo_AllVideosPayload";
const HASH_ALL = "#all-videos";

type ShowAllPayload = {
  section: string;
  data: DocumentItem[];
};

const setViewParam = (value?: string): void => {
  const url = new URL(window.location.href);
  if (value) {
    url.searchParams.set(VIEW_PARAM, value);
    // If a search query exists, clear it so it doesn't override the view
    url.searchParams.delete("q");
  } else {
    url.searchParams.delete(VIEW_PARAM);
  }
  window.history.pushState({}, "", url.toString());
};

const KeyUserInfo: React.FC<IKeyUserInfoProps> = (_props) => {
  const { strings } = useLocale();

  const [showAllVideos, setShowAllVideos] = useState<ShowAllPayload | null>(
    null
  );

  const componentRef = React.useRef<HTMLDivElement>(null);

  // Restore state on first mount if URL/hash + storage indicate "All Videos"
  React.useEffect(() => {
    if (window.location.hash === HASH_ALL) {
      try {
        const raw = sessionStorage.getItem(STORAGE_KEY);
        if (raw) {
          const parsed = JSON.parse(raw) as {
            section: string;
            data: DocumentItem[];
          };
          if (parsed?.section && Array.isArray(parsed?.data)) {
            setShowAllVideos(parsed);
            // Make sure we scroll to the component after paint
            setTimeout(() => {
              componentRef.current?.scrollIntoView({
                behavior: "auto",
                block: "start",
              });
            }, 0);
          }
        }
      } catch {
        // ignore parse errors and fall back to default
      }
    }
  }, []);

  const handleReadMore = (
    viewId: string,
    section: string,
    data: DocumentItem[]
  ): void => {
    const payload: ShowAllPayload = { section, data };
    setShowAllVideos(payload);
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(payload));
    setViewParam(viewId);

    setTimeout(() => {
      componentRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  const handleBack = (): void => {
    setShowAllVideos(null);
    // Clear persisted view
    sessionStorage.removeItem(STORAGE_KEY);
    // Remove the hash from the URL
    if (window.location.hash === HASH_ALL) {
      history.replaceState(
        null,
        "",
        `${window.location.pathname}${window.location.search}`
      );
    }
  };

  if (showAllVideos) {
    return (
      <AllVideosView
        title={showAllVideos.section}
        docData={showAllVideos.data}
        onBack={handleBack}
      />
    );
  }

  return (
    <div ref={componentRef} className="section-common sectionHandbooks">
      {/* Header */}
      <div className={styles.customHeader}>
        <div>
          <h2 className={styles.pageTitle}>{strings.Common.KeyUserTitle}</h2>
          <p className={styles.pageSubtitle}>
            {strings.Common.KeyUserSubtitle}
          </p>
        </div>
      </div>

      <div id="short-trips">
        <VideoShortsCarousel
          onReadMore={(data): void =>
            handleReadMore(
              "short-trips",
              strings.HeaderCarousel.Cards.ShortTrips,
              data.map((item) => ({
                Title: item.Title,
                Videos: item.Videos,
                PreviewUrl: item.PreviewUrl,
              }))
            )
          }
        />
      </div>
      
      {/* In-depth Sessions */}
      <div id="depth-session">
        <VideoCarousel
          onReadMore={(data) =>
            handleReadMore(
              "depth-session",
              strings.HeaderCarousel.Cards.DepthSessions,
              data.map((item: VideoItem) => ({
                Title: item.SubTitle || item.subTitle || "",
                Videos: item.Videos || item.videos || "",
                PreviewUrl:
                  item.PreviewUrl || item.previewUrl || item.url || "",
                Thumbnail: item.Thumbnail || "",
              }))
            )
          }
        />
      </div>

      {/* Module Series */}
      <div id="module-series">
        <ModuleSeries
          onReadMore={(data) =>
            handleReadMore(
              "module-series",
              strings.HeaderCarousel.Cards.ModuleSeries,
              data.map((item: VideoItem) => ({
                Title: item.SubTitle || item.subTitle || "",
                Videos: item.Videos || item.videos || "",
                PreviewUrl:
                  item.PreviewUrl || item.previewUrl || item.url || "",
                Thumbnail: item.Thumbnail || "",
              }))
            )
          }
        />
      </div>

      {/* Access PLATON / Demos */}
      <div id="system-demos">
        <PlatonDemo
          onReadMore={(data) =>
            handleReadMore(
              "system-demos",
              strings.HeaderCarousel.Cards.AccessPlaton,
              data
            )
          }
        />
      </div>

      {/* Manuals */}
      <div id="manuals">
        <ModuleShorts
          onReadMore={(data) =>
            handleReadMore(
              "manuals",
              strings.HeaderCarousel.Cards.Manuals,
              data
            )
          }
        />
      </div>

      {/* Test Cases */}
      <div id="test-cases">
        <TestCases
          onReadMore={(data) =>
            handleReadMore(
              "test-cases",
              strings.HeaderCarousel.Cards.TestCase,
              data
            )
          }
        />
      </div>
    </div>
  );
};

export default KeyUserInfo;
