import enUS from "../common/locales/en-us";
import jaJP from "../common/locales/ja-jp";

export type LocaleStrings = typeof enUS;

const localeMap: Record<'en' | 'jp', LocaleStrings> = {
    en: enUS,
    jp: jaJP,
};

export function getCurrentLang(): "en" | "jp" {
    return (localStorage.getItem("platonLang") as "en" | "jp") || "en";
}

export function getStrings(lang?: "en" | "jp"): LocaleStrings {
    const currentLang = lang || getCurrentLang();
    return localeMap[currentLang];
}