import * as React from "react";
import styles from "./indepthSession.module.scss";
import { useLocale } from "../../../common/useLocale";
// import PlatonLogo from "./../assets/PLATON_Logo.svg";

interface VideoItem {
  Title: string;
  SubTitle: string;
  Duration: string;
  Videos: string;
  Thumbnail: string;
}

interface SharePointRawItem {
  Title: string;
  SubTitle?: string;
  Duration?: string;
  Videos?: { Url: string };
  Thumbnail?: string;
}

interface SharePointResponse {
  d: {
    results: SharePointRawItem[];
  };
}

interface VideoCarouselProps {
  onReadMore: (data: VideoItem[]) => void;
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const listName = "platonindepthsession";

const VideoCarousel: React.FC<VideoCarouselProps> = ({ onReadMore }) => {
  const [videoData, setVideoData] = React.useState<VideoItem[]>([]);
  const [startIndex, setStartIndex] = React.useState(0);
  const visibleCount = 4;
  const { lang, strings } = useLocale();

  React.useEffect(() => {
    const fetchVideos = async (): Promise<void> => {
      try {
        const currentLang = localStorage.getItem("platonLang") || "en";
        const sharePointLang = currentLang === "jp" ? "JP" : "EN";

        const response = await fetch(
          `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$select=Title,SubTitle,Duration,Videos,Thumbnail,Language&$filter=Language eq '${sharePointLang}'`,
          {
            headers: { Accept: "application/json;odata=verbose" },
            credentials: "include",
          }
        );

        const data: SharePointResponse = await response.json();
        const videos: VideoItem[] = data.d.results.map((item) => ({
          Title: item.Title,
          SubTitle: item.SubTitle || "",
          Duration: item.Duration || "",
          Videos: item.Videos?.Url || "",
          Thumbnail: item.Thumbnail || "",
        }));
        setVideoData(videos);
      } catch (error) {
        console.error("Error fetching videos:", error);
      }
    };

    fetchVideos().catch(console.error);
  }, [lang]);

  const pageCount = Math.ceil(videoData.length / visibleCount);
  const activePage = Math.floor(startIndex / visibleCount);

  const visibleItems = videoData.slice(startIndex, startIndex + visibleCount);

  const goToPage = (pageIndex: number): void => {
    setStartIndex(pageIndex * visibleCount);
  };

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + visibleCount < videoData.length ? prev + visibleCount : prev
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - visibleCount >= 0 ? prev - visibleCount : prev
    );
  };

  return (
    <>
      <div className={`${"section-common sectionHandbooks"}`}>
        <div className={`${styles.carouselHeader} ${"sectionHeading"}`}>
          <h2>{strings.HeaderCarousel.PLATONDepthSessions}</h2>
          <div className={styles.navButtons}>
            <button onClick={goToPrevious} aria-label="Previous">
              &#9664;
            </button>
            <button onClick={goToNext} aria-label="Next">
              &#9654;
            </button>
          </div>
        </div>

        <div className={styles.videoCarousel}>
          <div className={styles.carouselContainer}>
            <div className={styles.carousel}>
              {visibleItems.map((item, index) => (
                <div className={styles.card} key={index}>
                  <a
                    href={`${item.Videos}?web=1`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className={styles.cardContent}>
                      <video controls width="100%" className={styles.videoTop} poster={item.Thumbnail}>
                        <source src={item.Videos} type="video/mp4" />
                      </video>

                      <div className={styles.textContent}>
                        <h3>{item.SubTitle}</h3>
                        <p>{strings.Common.DurationLabel}: {item.Duration}</p>
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>

            <div className={styles.paginationDots}>
              {Array.from({ length: pageCount }).map((_, idx) => (
                <button
                  key={idx}
                  className={`${styles.dot} ${
                    idx === activePage ? styles.activeDot : ""
                  }`}
                  onClick={() => goToPage(idx)}
                  aria-label={`Go to page ${idx + 1}`}
                />
              ))}
            </div>
          </div>

          <div className={styles.readMoreContainer}>
            <button
              className={styles.readMoreButton}
              onClick={() => onReadMore(videoData)}
            >
              {strings.Common.ReadMore}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default VideoCarousel;
