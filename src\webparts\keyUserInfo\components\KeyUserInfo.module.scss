.allVideosContainer {
  margin-left: 50px;
  background-color: #fff;
}

.customHeader {
  background-color: #0d1525;
  color: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.pageTitle {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.pageSubtitle {
  margin: 5px 0;
  font-size: 16px;
  color: #ccc;
}

.totalCount {
  font-size: 14px;
  color: #ccc;
}

.searchSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.stats {
  font-size: 14px;
  color: #888787;
}

.searchWrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 250px;
}

.searchIcon {
  position: absolute;
  left: 10px;
  color: #888;
  pointer-events: none;
  font-size: 14px;
}

.clearIcon {
  position: absolute;
  right: 10px;
  color: #888;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s ease;
}

.clearIcon:hover {
  color: #000;
}

.searchInput {
  width: 100%;
  padding: 8px 30px 8px 32px; // right padding for X icon, left for search icon
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #ccc;
}

.backButton {
  background-color: #fcd32a;
  color: #000;
  border: none;
  padding: 10px 20px;
  font-weight: bold;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.backButton:hover {
  background-color: #ffd900;
}

.videoGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-top: 20px;
  .card {
    border: 1px solid #c9c9c9;
    border-radius: 8px;
    box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    overflow: hidden;
    padding: 10px;
    transition: transform 0.3s ease;
    .title {
      font-size: 14px;
      font-weight: 600;
      padding: 10px 0;
      text-align: center;
    }
    &:hover {
      transform: scale(1.05);
    }
  }
}

.previewImage {
  width: 100%;
  object-fit: contain; // Ensures the image is not stretched
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.noResults {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin-top: 20px;
  width: 100%;
}

.loadMoreContainer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.loadMoreButton {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
  border: 1px solid #ccc;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.loadMoreButton:hover {
  background-color: #ddd;
}

.backToTopContainer {
  text-align: center;
  margin-top: 20px;
}

.backToTopButton {
  background-color: #ffd700;
  border: none;
  padding: 10px 20px;
  font-weight: bold;
  font-size: 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #ffc107;
  }
}

:global {
  .categoryCarouselWrapper {
    .category-carousel-container {
      .category-carousel-cards-group {
        & > div[class*="card"] {
          box-shadow: none;
          border: 0;
          background: none;
          padding: 0;
          figure {
            height: 140px;
            margin: 0 0 0 12px;
            padding: 20px 0;
            width: calc(100% - 30px);
            background: #2b2b2b;
            background: radial-gradient(
              circle,
              rgba(43, 43, 43, 1) 0%,
              rgba(0, 0, 0, 1) 100%
            );
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            position: relative;
            top: 10px;
            border: 3px solid #fff;
            img {
              width: auto;
              height: 100%;
              margin: 0;
            }
          }
          .cardContent,
          .card-content-wrapper {
            background-color: #fff;
            padding: 12px;
            text-align: center;
            border: 1px solid #c9c9c9;
            border-radius: 8px;
            box-shadow: inset 0 0 15px 0 rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, background-color 0.3s ease;
            h3 {
              font-weight: 600;
            }
            & > div[class*="likes"] {
              justify-content: center;
            }
          }
          &:hover {
            figure {
              background: #000000;
              background: linear-gradient(
                0deg,
                rgba(0, 0, 0, 1) 0%,
                rgba(79, 79, 79, 1) 100%
              );
            }
            .card-content-wrapper {
              box-shadow: none;
              border-color: #212121;
              background-color: #212121;
              color: #ff0;
              h3 {
                font-weight: 700;
              }
              & > * {
                color: #ff0;
              }
              & > div[class*="likes"] {
                color: #ff0;
              }
            }
            .cardContent {
              h3 {
                font-weight: 700;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive Styles Begin Here
@media screen and (max-width: 1260px) {
  .videoCarousel {
    .carouselContainer {
      .carousel {
        grid-template-columns: auto;
      }
    }
  }
  .videoGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// /*Tablet*/
// @media (max-width: 992px) {
//   .card {
//     width: 48%; /* 2 per row */
//   }
// }

/*767 Pixel*/
@media screen and (max-width: 767px) {
  .carouselHeader {
    flex-direction: column;
    gap: 10px;
  }
  .videoGrid {
    grid-template-columns: auto;
  }
}

/*Smaller Screen*/
@media (max-width: 600px) {
  .card {
    width: 100%; /* 1 per row */
  }
  .carouselHeader {
    flex-direction: column;
  }
}
