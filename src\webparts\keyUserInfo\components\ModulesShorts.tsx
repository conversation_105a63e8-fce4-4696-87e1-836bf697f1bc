import * as React from "react";
import styles from "./indepthSession.module.scss";
import { useLocale } from "../../../common/useLocale";

interface DocumentItem {
  Title: string;
  Videos: string; // Document URL
  PreviewUrl: string; // First-page preview image URL
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const listNameEng = "platonmanualseng";
const listNameJap = "platonmanualsjap";
const visibleCount = 4;
const maxInitialItems = 8;

interface SharePointItem {
  Title: string;
  Deck?: {
    Url?: string;
  };
}

interface ModulesShortsProps {
  onReadMore: (data: DocumentItem[]) => void;
}

const ModulesShorts: React.FC<ModulesShortsProps> = ({ onReadMore }) => {
  const [docData, setDocData] = React.useState<DocumentItem[]>([]);
  const [startIndex, setStartIndex] = React.useState(0);
  const { lang, strings } = useLocale();

  React.useEffect(() => {
    const fetchFromList = async (listName: string): Promise<DocumentItem[]> => {
      const response = await fetch(
        `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$select=Title,Deck`,
        {
          headers: { Accept: "application/json;odata=verbose" },
          credentials: "include",
        }
      );
      const data: { d: { results: SharePointItem[] } } = await response.json();
      return data.d.results.map((item) => {
        const docUrl = item.Deck?.Url || "";
        const previewUrl = `${siteUrl}/_layouts/15/getpreview.ashx?path=${encodeURIComponent(
          docUrl
        )}`;
        return {
          Title: item.Title,
          Videos: docUrl,
          PreviewUrl: previewUrl,
        };
      });
    };

    const fetchDocuments = async (): Promise<void> => {
      try {
        const currentLang = localStorage.getItem("platonLang") || "en";
        const listToFetch = currentLang === "jp" ? listNameJap : listNameEng;

        const documents = await fetchFromList(listToFetch);
        setDocData(documents);
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };

    fetchDocuments().catch((err) => console.error("Fetch failed", err));
  }, [lang]);

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + visibleCount >= Math.min(docData.length, maxInitialItems)
        ? 0
        : prev + visibleCount
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - visibleCount < 0
        ? Math.max(Math.min(docData.length, maxInitialItems) - visibleCount, 0)
        : prev - visibleCount
    );
  };

  const visibleItems = docData
    .slice(0, Math.min(docData.length, maxInitialItems))
    .slice(startIndex, startIndex + visibleCount);

  const pageCount = Math.ceil(
    Math.min(docData.length, maxInitialItems) / visibleCount
  );
  const activePage = Math.floor(startIndex / visibleCount);

  return (
    <div className="section-common sectionHandbooks">
      <div className={`${styles.carouselHeader} sectionHeading`}>
        <h2>{strings.HeaderCarousel.PLATONManuals}</h2>
        <div className={styles.navButtons}>
          <button onClick={goToPrevious} aria-label="Previous">
            &#9664;
          </button>
          <button onClick={goToNext} aria-label="Next">
            &#9654;
          </button>
        </div>
      </div>

      <div className={`${styles.videoCarousel} sectionHandbookPrimary`}>
        <div className={`${styles.carouselContainer} sectionHandbookContainer`}>
          <div className={`${styles.carousel} sectionHandbookCarousel`}>
            {visibleItems.map((item, index) => (
              <div className={styles.card} key={index}>
                <a
                  href={`${item.Videos}?web=1`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.previewLink}
                >
                  <div
                    className={`${styles.cardContent} sectionHandbookCardContent`}
                  >
                    <div className="contentImageWrapper">
                      <img
                        src={item.PreviewUrl}
                        alt={`Preview of ${item.Title}`}
                        className={styles.previewImage}
                      />
                    </div>
                    <div
                      className={`${styles.textContent} section-handbook-text-info`}
                    >
                      <h3>{item.Title}</h3>
                    </div>
                  </div>
                </a>
              </div>
            ))}
          </div>

          <div className={styles.paginationDots}>
            {Array.from({ length: pageCount }).map((_, idx) => (
              <button
                key={idx}
                className={`${styles.dot} ${
                  idx === activePage ? styles.activeDot : ""
                }`}
                onClick={() => setStartIndex(idx * visibleCount)}
                aria-label={`Go to page ${idx + 1}`}
              />
            ))}
          </div>
        </div>

        <div className={`${styles.readMoreContainer} section-read-more`}>
          <button
            onClick={() => onReadMore(docData)}
            className={styles.readMoreButton}
          >
            {strings.Common.ReadMore}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModulesShorts;
