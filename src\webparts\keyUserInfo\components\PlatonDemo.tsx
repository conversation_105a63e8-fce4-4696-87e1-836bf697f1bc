import * as React from "react";
import styles from "./indepthSession.module.scss";
import { useLocale } from "../../../common/useLocale";

interface DocumentItem {
  Title: string;
  Videos: string; // Document URL
  PreviewUrl: string; // First-page preview image URL
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const listName = "platoninformationhandbooks";
interface SharePointRawItem {
  Title: string;
  Deck?: { Url?: string };
  // add other properties if needed
}

interface PlatonDemoProps {
  onReadMore: (data: DocumentItem[]) => void;
}

const PlatonDemo: React.FC<PlatonDemoProps> = ({ onReadMore }) => {
  const [docData, setDocData] = React.useState<DocumentItem[]>([]);
  const [startIndex, setStartIndex] = React.useState(0);
  const visibleCount = 4;
  const { lang, strings } = useLocale();

  React.useEffect(() => {
    const fetchDocuments = async (): Promise<void> => {
      try {
        const currentLang = localStorage.getItem("platonLang") || "en";
        const sharePointLang = currentLang === "jp" ? "JP" : "EN";

        const response = await fetch(
          `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$select=Title,Deck,Language&$filter=Language eq '${sharePointLang}'`,
          {
            headers: { Accept: "application/json;odata=verbose" },
            credentials: "include",
          }
        );

        const data: { d: { results: SharePointRawItem[] } } =
          await response.json();

        const documents: DocumentItem[] = data.d.results.map(
          (item: SharePointRawItem) => {
            const docUrl = item.Deck?.Url || "";
            const previewUrl = `/_layouts/15/getpreview.ashx?path=${encodeURIComponent(
              docUrl
            )}`;
            return {
              Title: item.Title,
              Videos: docUrl,
              PreviewUrl: previewUrl,
            };
          }
        );

        setDocData(documents);
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };

    fetchDocuments().catch((error) => {
      console.error("Unhandled error in fetchDocuments:", error);
    });
  }, [lang]);

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + visibleCount >= docData.length ? 0 : prev + visibleCount
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - visibleCount < 0
        ? Math.max(docData.length - visibleCount, 0)
        : prev - visibleCount
    );
  };

  const visibleItems = docData.slice(startIndex, startIndex + visibleCount);
  const pageCount = Math.ceil(docData.length / visibleCount);
  const activePage = Math.floor(startIndex / visibleCount);

  return (
    <>
      <div className={`${"section-common sectionHandbooks"}`}>
        <div className={`${styles.carouselHeader} ${"sectionHeading"}`}>
          <h2>{strings.HeaderCarousel.PLATONAccessPlaton}</h2>

          <div className={styles.navButtons}>
            <button onClick={goToPrevious} aria-label="Previous">
              &#9664;
            </button>
            <button onClick={goToNext} aria-label="Next">
              &#9654;
            </button>
          </div>
        </div>

        <div className={`${styles.videoCarousel} ${"sectionHandbookPrimary"}`}>
          <div
            className={`${
              styles.carouselContainer
            } ${"sectionHandbookContainer"}`}
          >
            <div className={`${styles.carousel} ${"sectionHandbookCarousel"}`}>
              {visibleItems.map((item, index) => (
                <div className={styles.card} key={index}>
                  <a
                    href={`${item.Videos}?web=1`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={styles.previewLink}
                  >
                    <div
                      className={`${
                        styles.cardContent
                      } ${"sectionHandbookCardContent"}`}
                    >
                      <div className={`${"contentImageWrapper"}`}>
                        <img
                          src={item.PreviewUrl}
                          alt={`Preview of ${item.Title}`}
                          className={styles.previewImage}
                        />
                      </div>
                      <div
                        className={`${
                          styles.textContent
                        } ${"section-handbook-text-info"}`}
                      >
                        <h3>{item.Title}</h3>
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>

            <div className={styles.paginationDots}>
              {Array.from({ length: pageCount }).map((_, idx) => (
                <button
                  key={idx}
                  className={`${styles.dot} ${
                    idx === activePage ? styles.activeDot : ""
                  }`}
                  onClick={() => setStartIndex(idx * visibleCount)}
                  aria-label={`Go to page ${idx + 1}`}
                />
              ))}
            </div>
          </div>

          <div className={`${styles.readMoreContainer} ${"section-read-more"}`}>
            <button
              onClick={() => onReadMore(docData)}
              className={styles.readMoreButton}
            >
              {strings.Common.ReadMore}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default PlatonDemo;
