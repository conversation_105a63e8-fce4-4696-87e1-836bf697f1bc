import * as React from "react";
import styles from "./VideoShortsCarousel.module.scss";
import { useLocale } from "../../../common/useLocale";

interface PlatonShortsCarouselProps {
  onReadMore: (data: DocumentItem[]) => void;
}

interface DocumentItem {
  Title: string;
  Videos: string;
  PreviewUrl: string;
}

const VISIBLE_COUNT = 5;
const IS_END_USER = "isEndUser";

type ODataVerbose<T> = { d: { results: T[] } };

type SPVideosField = string | { Url?: string };

interface SPShortItem {
  Title: string;
  Videos?: SPVideosField;
  Language?: string;
  isEndUser?: boolean | number; // flag may come as 0/1 or true/false
}

const PlatonShortsCarousel: React.FC<PlatonShortsCarouselProps> = ({
  onReadMore,
}) => {
  const [startIndex, setStartIndex] = React.useState(0);
  const [shortsData, setShortsData] = React.useState<DocumentItem[]>([]);
  const [isKeyUser, setIsKeyUser] = React.useState<boolean | null>(null);
  const pageCount = Math.ceil(shortsData.length / VISIBLE_COUNT);
  const activePage = Math.floor(startIndex / VISIBLE_COUNT);
  const { lang, strings } = useLocale();

  React.useEffect(() => {
    const raw = localStorage.getItem("isKeyUser"); // "true" | "false" | null
    const v = (raw ?? "false").toLowerCase() === "true";
    setIsKeyUser(v);
    console.log("[PLATON] isKeyUser:", v, "(raw:", raw, ")");
  }, []);

  React.useEffect(() => {
    if (isKeyUser === null) return; // wait until known

    const fetchShorts = async (): Promise<void> => {
      try {
        const currentLang = localStorage.getItem("platonLang") || "en";
        const sharePointLang = currentLang === "jp" ? "JP" : "EN";

        const select = `Title,Videos,Language,${IS_END_USER}`;

        // ✅ Only add end-user filter when NOT a key user
        const filters: string[] = [`Language eq '${sharePointLang}'`];
        console.log("Language:", filters);
        // if (!isKeyUser) filters.push(`${IS_END_USER} eq false`);
        // const filter = filters.join(" and ");
        // console.log("After Filter:", filter);

        const url =
          `https://corptb.sharepoint.com/sites/ProjectEngLand/_api/web/lists/getbytitle('platonshorts')/items` +
          `?$select=${select}&$filter=${filters}&$top=5000`;

        console.log("[PLATON] URL:", url);

        const res = await fetch(url, {
          headers: { Accept: "application/json;odata=verbose" },
          credentials: "include",
        });
        const data: ODataVerbose<SPShortItem> = await res.json();
        const rows: SPShortItem[] = data.d?.results ?? [];

        // 🔒 Client-side guard (just in case server filter misfires)
        const safeRows = isKeyUser
          ? rows
          : rows.filter((r) => r[IS_END_USER] === true || r[IS_END_USER] === 1);

        const items: DocumentItem[] = safeRows.map((item) => {
          const v =
            typeof item.Videos === "string"
              ? item.Videos
              : item.Videos?.Url ?? "";
          return {
            Title: item.Title,
            Videos: v,
            PreviewUrl: `/_layouts/15/getpreview.ashx?path=${encodeURIComponent(
              v
            )}`,
          };
        });

        setShortsData(items);
        setStartIndex(0);
        console.log("[PLATON] Final count:", items.length);
      } catch (e) {
        console.error("Error fetching shorts data:", e);
        setShortsData([]);
      }
    };

    // Fix "no-floating-promises"
    fetchShorts().catch((err) => {
      console.error("fetchShorts failed:", err);
      setShortsData([]);
    });
  }, [lang, isKeyUser]);

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + VISIBLE_COUNT < shortsData.length ? prev + VISIBLE_COUNT : prev
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - VISIBLE_COUNT >= 0 ? prev - VISIBLE_COUNT : prev
    );
  };

  const visibleItems = shortsData.slice(startIndex, startIndex + VISIBLE_COUNT);

  return (
    <>
      <div className={`${"section-common section-short-trips"}`}>
        <div className={`${styles.carouselHeader} ${"sectionHeading"}`}>
          <h2>{strings.HeaderCarousel.PLATONShortTrips}</h2>
          <div className={styles.navButtons}>
            <button onClick={goToPrevious} aria-label="Previous">
              &#9664;
            </button>
            <button onClick={goToNext} aria-label="Next">
              &#9654;
            </button>
          </div>
        </div>

        <div className={styles.shortsGrid}>
          {visibleItems.map((short, index) => (
            <div className={styles.shortsTile} key={index}>
              <a
                href={`${short.Videos}?web=1`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <video src={short.Videos} controls muted />
                <div className={styles.shortsGridContent}>
                  <div className={styles.shortsTitle}>{short.Title}</div>
                </div>
              </a>
            </div>
          ))}
        </div>

        <div className={styles.paginationDots}>
          {Array.from({ length: pageCount }).map((_, idx) => (
            <button
              key={idx}
              className={`${styles.dot} ${
                idx === activePage ? styles.activeDot : ""
              }`}
              onClick={() => setStartIndex(idx * VISIBLE_COUNT)}
              aria-label={`Go to page ${idx + 1}`}
            />
          ))}
        </div>

        <div className={styles.readMoreContainer}>
          <button
            onClick={() =>
              onReadMore(
                shortsData.map((item) => ({
                  Title: item.Title,
                  Videos: item.Videos,
                  PreviewUrl: item.PreviewUrl,
                }))
              )
            }
            className={styles.readMoreButton}
          >
            {strings.Common.ReadMore}
          </button>
        </div>
      </div>
    </>
  );
};

export default PlatonShortsCarousel;
