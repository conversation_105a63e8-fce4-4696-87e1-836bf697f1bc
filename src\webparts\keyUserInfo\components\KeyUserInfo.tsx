// KeyUserInfo.tsx
import * as React from "react";
import styles from "./KeyUserInfo.module.scss";
import { IKeyUserInfoProps } from "./IKeyUserInfoProps";
import VideoCarousel from "./InDepthSession";
import ModuleSeries from "./ModuleSeries";
import PlatonDemo from "./PlatonDemo";
import ModuleShorts from "./ModulesShorts";
import TestCases from "./TestCases";
import { useLocale } from "../../../common/useLocale";
import AllVideosView from "./AllVideosView";

interface DocumentItem {
  Title: string;
  Videos: string;
  PreviewUrl: string;
}
interface VideoItem {
  Title?: string;
  Videos?: string;
  PreviewUrl?: string;
  title?: string;
  videos?: string;
  previewUrl?: string;
  url?: string;
}

// interface TestCaseItem {
//   Title: string;
//   Videos: string;
//   PreviewUrl: string;
// }

const KeyUserInfo: React.FC<IKeyUserInfoProps> = (_props) => {
  const { strings } = useLocale();

  const [showAllVideos, setShowAllVideos] = React.useState<{
    section: string;
    data: DocumentItem[];
  } | null>(null);

  const componentRef = React.useRef<HTMLDivElement>(null);

  const handleReadMore = (section: string, data: DocumentItem[]): void => {
    setShowAllVideos({ section, data });
    setTimeout(() => {
      componentRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }, 100);
  };

  if (showAllVideos) {
    return (
      <AllVideosView
        title={showAllVideos.section}
        docData={showAllVideos.data}
        onBack={() => setShowAllVideos(null)}
      />
    );
  }

  return (
    <div ref={componentRef} className="section-common sectionHandbooks">
      {/* Header */}
      <div className={styles.customHeader}>
        <div>
          <h2 className={styles.pageTitle}>Key Users Info</h2>
          <p className={styles.pageSubtitle}>
            Explore all our video tutorials and guides
          </p>
        </div>
      </div>

      {/* In-depth Sessions */}
      <div id="depth-session">
        <VideoCarousel
          onReadMore={(data: VideoItem[]) =>
            handleReadMore(
              strings.HeaderCarousel.Cards.DepthSessions,
              data.map((item) => ({
                Title: item.Title || item.title || "",
                Videos: item.Videos || item.videos || "",
                PreviewUrl:
                  item.PreviewUrl || item.previewUrl || item.url || "",
              }))
            )
          }
        />
      </div>

      {/* Module Series */}
      <div id="module-series">
        <ModuleSeries
          onReadMore={(data) =>
            handleReadMore(
              strings.HeaderCarousel.Cards.ModuleSeries,
              data.map((item) => ({
                Title: item.Title,
                Videos: item.Videos,
                PreviewUrl: `/_layouts/15/getpreview.ashx?path=${encodeURIComponent(
                  item.Videos || ""
                )}`,
              }))
            )
          }
        />
      </div>

      {/* Access PLATON / Demos */}
      <div id="system-demos">
        <PlatonDemo
          onReadMore={(data) =>
            handleReadMore(strings.HeaderCarousel.Cards.AccessPlaton, data)
          }
        />
      </div>

      {/* Manuals */}
      <div id="manuals">
        <ModuleShorts
          onReadMore={(data) =>
            handleReadMore(strings.HeaderCarousel.Cards.Manuals, data)
          }
        />
      </div>

      {/* Test Cases */}
      <div id="test-cases">
        <TestCases onReadMore={(data) => handleReadMore("Test Cases", data)} />
      </div>
    </div>
  );
};

export default KeyUserInfo;
