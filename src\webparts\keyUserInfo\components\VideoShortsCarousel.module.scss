.carouselHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.navButtons {
  button {
    cursor: pointer;
    font-size: 10px;
    margin-left: 10px;
    border: 1px solid #848484;
    background: #fff;
    color: #848484;
    padding: 10px;
    border-radius: 5px;
    &:hover {
      border: 1px solid #000;
      color: #ff0;
      background: #000;
    }
  }
}

.readMoreContainer {
  position: absolute;
  right: 0;
  bottom: 0;
}

.readMoreButton {
  color: #000;
  padding: 8px 20px;
  font-weight: normal;
  border: 1px solid #c6c6c6;
  font-size: 12px;
  border-radius: 30px;
  font-weight: bold;
  background: #ffd700;
  transition: background-color 0.3s ease;
  text-decoration: none;
}

.readMoreButton:hover {
  border-color: #000;
  background-color: #000;
  color: #ffd700;
}

.shortsGridWrapper {
  overflow: hidden;
}

.shortsGrid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  width: 100%;
  transition: transform 0.5s ease-in-out;
  .shortsTile {
    overflow: hidden;
    transition: transform 0.3s ease;
    border-radius: 8px;
    box-shadow: 0 0 10px gray;
    box-sizing: border-box;
    padding: 10px;
    a {
      text-decoration: none;
      color: #000;
      video {
        width: 100%;
        height: 350px;
        object-fit: cover;
        border-bottom: 1px solid #ddd;
      }
      .shortsGridContent {
        display: block;
        text-align: center;
        .shortsTitle {
          font-size: 14px;
          font-weight: 600;
          padding: 0;
          text-align: center;
        }
      }
      &:hover {
        transform: scale(1.05);
      }
    }
  }
}

.stars {
  padding: 0;
  color: #ffd700;
}

.star {
  font-size: 16px;
}

.paginationDots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 0;
  gap: 10px;
}

.dot {
  height: 14px;
  width: 14px;
  background-color: #000;
  border-radius: 100%;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  border: 1px solid #000;
  &:hover {
    background-color: #999;
  }
}

.activeDot {
  background-color: #ff0 !important;
}

// Responsive Styles Begin Here
@media screen and (max-width: 1260px) {
  .shortsGrid {
    grid-template-columns: auto;
  }
}
@media screen and (max-width: 767px) {
  .carouselHeader {
    flex-direction: column;
    gap: 10px;
  }
}
