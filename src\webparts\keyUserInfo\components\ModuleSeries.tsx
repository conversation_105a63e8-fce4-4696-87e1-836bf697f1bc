import * as React from "react";
import styles from "./indepthSession.module.scss";
import { useLocale } from "../../../common/useLocale";
// import PlatonLogo from "../../platonHeaderCarousel/assets/PLATON_Logo.svg";

interface VideoItem {
  Title: string;
  SubTitle: string;
  Duration: string;
  Videos: string;
  PreviewUrl: string;
}

interface SharePointRawItem {
  Title: string;
  SubTitle?: string;
  Duration?: string;
  Videos?: { Url: string };
}

interface ModuleSeriesProps {
  onReadMore: (data: VideoItem[]) => void;
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const listName = "platonmoduleseries";
const visibleCount = 4;
const maxInitialItems = 8;

const ModuleSeries: React.FC<ModuleSeriesProps> = ({ onReadMore }) => {
  const [videoData, setVideoData] = React.useState<VideoItem[]>([]);
  const [startIndex, setStartIndex] = React.useState(0);
  const { lang, strings } = useLocale();

  React.useEffect(() => {
    async function fetchVideos(): Promise<void> {
      try {
        const currentLang = localStorage.getItem("platonLang") || "en";
        const sharePointLang = currentLang === "jp" ? "JP" : "EN";

        const response = await fetch(
          `${siteUrl}/_api/web/lists/getbytitle('${listName}')/items?$select=Title,SubTitle,Duration,Videos,Language&$filter=Language eq '${sharePointLang}'`,
          {
            headers: { Accept: "application/json;odata=verbose" },
            credentials: "include",
          }
        );

        const data: { d: { results: SharePointRawItem[] } } =
          await response.json();

        const videos: VideoItem[] = data.d.results.map(
          (item: SharePointRawItem) => ({
            Title: item.Title,
            SubTitle: item.SubTitle || "",
            Duration: item.Duration || "",
            Videos: item.Videos?.Url || "",
            PreviewUrl: "", // Set dynamically later if needed
          })
        );

        setVideoData(videos);
      } catch (error) {
        console.error("Error fetching videos:", error);
      }
    }

    fetchVideos().catch((error) => console.error("Unhandled error:", error));
  }, [lang]);

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + visibleCount >= Math.min(videoData.length, maxInitialItems)
        ? 0
        : prev + visibleCount
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - visibleCount < 0
        ? Math.max(
            Math.min(videoData.length, maxInitialItems) - visibleCount,
            0
          )
        : prev - visibleCount
    );
  };

  const visibleItems = videoData
    .slice(0, Math.min(videoData.length, maxInitialItems))
    .slice(startIndex, startIndex + visibleCount);

  const pageCount = Math.ceil(
    Math.min(videoData.length, maxInitialItems) / visibleCount
  );
  const activePage = Math.floor(startIndex / visibleCount);

  return (
    <>
      <div className={`${"section-common sectionHandbooks"}`}>
        <div className={`${styles.carouselHeader} ${"section-heading"}`}>
          <h2>{strings.HeaderCarousel.PLATONModuleSeries}</h2>

          <div className={styles.navButtons}>
            <button onClick={goToPrevious} aria-label="Previous">
              &#9664;
            </button>
            <button onClick={goToNext} aria-label="Next">
              &#9654;
            </button>
          </div>
        </div>

        <div className={styles.videoCarousel}>
          <div className={styles.carouselContainer}>
            <div className={styles.carousel}>
              {visibleItems.map((item, index) => (
                <div className={styles.card} key={index}>
                  <a
                    href={`${item.Videos}?web=1`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className={styles.cardContent}>
                      {item.Videos.endsWith(".mp4") ? (
                        <video
                          controls
                          width="100%"
                          className={styles.previewImage}
                        //   poster={PlatonLogo}
                        >
                          <source src={item.Videos} type="video/mp4" />
                        </video>
                      ) : item.Videos.endsWith(".wmv") ? (
                        <video
                          controls
                          width="100%"
                          className={styles.previewImage}
                        //   poster={PlatonLogo}
                        >
                          <source src={item.Videos} type="video/mp4" />
                        </video>
                      ) : (
                        <img
                          src={item.PreviewUrl}
                          alt={`Preview of ${item.Title}`}
                          className={styles.previewImage}
                        />
                      )}

                      <div className={styles.textContent}>
                        <h3>{item.SubTitle}</h3>
                        <p>Duration: {item.Duration}</p>
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>

            <div className={styles.paginationDots}>
              {Array.from({ length: pageCount }).map((_, idx) => (
                <button
                  key={idx}
                  className={`${styles.dot} ${
                    idx === activePage ? styles.activeDot : ""
                  }`}
                  onClick={() => setStartIndex(idx * visibleCount)}
                  aria-label={`Go to page ${idx + 1}`}
                />
              ))}
            </div>
          </div>

          <div className={styles.readMoreContainer}>
            <button
              onClick={() => onReadMore(videoData)}
              className={styles.readMoreButton}
            >
              {strings.Common.ReadMore}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default ModuleSeries;
