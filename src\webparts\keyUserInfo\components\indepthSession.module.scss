.videoCarousel {
    text-align: center;
    position: relative;
    .carouselContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      max-width: 100%;
      margin: auto;
      .carousel {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        width: 100%;
        gap: 20px;
        .card {
          flex: 0 0 23.5%;
          padding: 10px;
          box-sizing: border-box;
          // border: 1px solid #c9c9c9;
          box-shadow:  0 0 10px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
          a {
            text-decoration: none;
            color: #000;
            .cardContent {
              padding: 15px 15px 10px;
              display: flex;
              flex-direction: column;
              height: 100%;
              .videoTop {
                border-radius: 6px;
                margin-bottom: 10px;
              }
            }
          }
        }
      }
    }
  }
  
  .textContent {
    text-align: left;
  }
  
  .textContent h3 {
    margin: 5px 0;
  }
  
  .textContent p {
    margin: 4px 0;
    font-size: 14px;
  }
  
  .carouselHeader {
    padding: 5px;
    font-weight: 800;
    font-size: larger;
    background: white;
    color: black;
  }
  
  /* Pagination dots */
  .paginationDots {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 0;
    gap: 10px;
    .dot {
      height: 14px;
      width: 14px;
      background-color: #000;
      border-radius: 100%;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s ease;
      border: 1px solid #000;
      &:hover {
        background-color: #999;
      }
    }
    .activeDot {
      background-color: #ff0 !important;
    }
  }
  .docLink {
    display: inline-block;
    padding: 10px 15px;
    background-color: #0078d4;
    color: #ffffff;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    transition: background-color 0.3s;
  }
  
  .docLink:hover {
    background-color: #005a9e;
  }
  .previewImage {
    width: 100%;
    // height: auto;
    // object-fit: contain; // Ensures the image is not stretched
    // max-height: 100px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
  }
  
  .previewImages {
    width: 100%;
    height: auto;
    object-fit: contain; // Ensures the image is not stretched
    max-height: 100px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease;
  }
  
  .previewImage:hover,
  .previewImages:hover {
    transform: scale(1.02);
  }
  
  .previewLink {
    display: block;
    text-decoration: none;
  }
  
  .carouselHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .carouselTitle {
    margin: 0;
    font-size: 20px;
  }
  
  .navButtons {
    button {
      cursor: pointer;
      font-size: 10px;
      margin-left: 10px;
      border: 1px solid #848484;
      background: #fff;
      color: #848484;
      padding: 10px;
      border-radius: 5px;
      &:hover {
        border: 1px solid #000;
        color: #ff0;
        background: #000;
      }
    }
  }
  
  .readMoreContainer {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  
  .readMoreButton {
    color: #000;
    padding: 8px 20px;
    font-weight: normal;
    border: 1px solid #c6c6c6;
    font-size: 12px;
    border-radius: 30px;
    font-weight: bold;
    background: #ffd700;
    transition: background-color 0.3s ease;
    text-decoration: none;
  }
  
  .readMoreButton:hover {
    border-color: #000;
    background-color: #000;
    color: #ffd700;
  }
  
  .error {
    color: red;
    text-align: center;
    font-weight: bold;
  }
  
  .noVideos {
    color: gray;
    text-align: center;
    margin-top: 1rem;
  }
  
  .shortsCarouselWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    .shortsGrid {
      display: grid;
      grid-template-columns: repeat(6, 160px);
      gap: 20px;
      width: 100%;
      .shortsTile {
        overflow: hidden;
        transition: transform 0.3s ease;
        .shortsContainer {
          border: 1px solid #c9c9c9;
          border-radius: 8px;
          text-align: center;
          padding: 10px;
          margin: 5px 0 0 0;
          position: relative;
          .shortsTitle {
            font-size: 14px;
            font-weight: 600;
            padding: 0;
            text-align: center;
          }
          .stars {
            color: #ffd700;
            padding: 0;
          }
        }
        &:hover {
          transform: scale(1.05);
          .shortsContainer {
            border: 1px dashed #c9c9c9;
            box-shadow: inset 0 0 15px 0 rgba(0, 0, 0, 0.2);
            &::before {
              border: 2px solid #fff;
              border-radius: 8px;
              content: "";
              height: calc(100% - 4px);
              left: 0px;
              position: absolute;
              top: 0;
              width: calc(100% - 4px);
            }
          }
        }
        video {
          width: 100%;
          height: 240px;
          object-fit: cover;
          border-radius: 8px;
        }
      }
    }
  }
  
  .star {
    font-size: 16px;
  }
  
  :global {
    .sectionHandbooks {
      margin-top: 30px;
      .sectionHandbookPrimary {
        .sectionHandbookContainer {
          .sectionHandbookCarousel {
            gap: 20px;
            & > div[class*="card"] {
              border: 0;
              box-shadow: none;
              padding: 0;
              flex: 0 0 23.75%;
              background: #000;
              .sectionHandbookCardContent {
                padding: 0;
                .contentImageWrapper {
                  display: block;
                  text-align: center;
                  border: 1px solid #c9c9c9;
                  background: #fff;
                  border-radius: 8px 8px 0 0;
                  height: 140px;
                  overflow: hidden;
                  padding: 10px;
                  a {
                    display: block;
                    height: 100%;
                    overflow: hidden;
                    img {
                      box-shadow: none;
                    }
                  }
                }
  
                .section-handbook-text-info {
                  text-align: center;
                  padding: 15px;
                  color: #fff;
                  h3 {
                    margin: 0;
                    font-size: 14px;
                    line-height: normal;
                    font-weight: normal;
                  }
                }
                &:hover {
                  .section-handbook-text-info {
                    color: #ff0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  @media screen and (max-width: 1260px) {
    .sectionHandbooks {
      margin-top: 30px;
      .sectionHandbookPrimary {
        .sectionHandbookContainer {
          .sectionHandbookCarousel {
            & > div[class*="card"] {
              flex: auto;
              .sectionHandbookCardContent {
                padding: 0;
                .contentImageWrapper {
                  height: auto;
                  a {
                    img {
                      width: auto;
                      height: auto;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  
  // Responsive Styles Begin Here
  @media screen and (max-width: 1260px) {
    .shortsGrid {
      grid-template-columns: auto;
    }
    .videoCarousel {
      .carouselContainer {
        .carousel {
          grid-template-columns: auto;
        }
      }
    }
  }
  
  @media screen and (max-width: 767px) {
    .carouselHeader {
      flex-direction: column;
      gap: 10px;
    }
  }
  