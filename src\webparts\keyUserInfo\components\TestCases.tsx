import * as React from "react";
import styles from "./KeyUserInfo.module.scss";

interface DocumentItem {
  Title: string;
  Videos: string;      // file/video url
  PreviewUrl: string;  // first-page preview (docs) / poster (videos)
}
interface SharePointRawItem {
  Title: string;
  Content?: { Url?: string };
}
interface SharePointResponse {
  d: { results: SharePointRawItem[] };
}

interface TestCasesProps {
  onReadMore: (data: DocumentItem[]) => void;
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const lists = ["platontestcasesexecution", "platontestcasesguide"];

const visibleCount = 4;
const maxInitialItems = 8;

const defaultDocImage =
  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/87/PDF_file_icon.svg/400px-PDF_file_icon.svg.png";

const isVideo = (url: string): boolean => {
  const u = (url || "").toLowerCase();
  return u.endsWith(".mp4") || u.endsWith(".wmv") || u.includes("/videomanifest");
};

const TestCases: React.FC<TestCasesProps> = ({ onReadMore }) => {
  const [allItems, setAllItems] = React.useState<DocumentItem[]>([]);
  const [startIndex, setStartIndex] = React.useState(0);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    const fetchDocuments = async (): Promise<void> => {
      try {
        const combined: DocumentItem[] = [];

        for (const list of lists) {
          const resp = await fetch(
            `${siteUrl}/_api/web/lists/getbytitle('${list}')/items?$select=Title,Content`,
            { headers: { Accept: "application/json;odata=verbose" }, credentials: "include" }
          );
          const data: SharePointResponse = await resp.json();

          const docs: DocumentItem[] = (data?.d?.results ?? []).map((item) => {
            const url = item.Content?.Url || "";
            const preview = `/_layouts/15/getpreview.ashx?path=${encodeURIComponent(url)}`;
            return { Title: item.Title, Videos: url, PreviewUrl: preview };
          });

          combined.push(...docs);
        }

        // Optional: sort by Title for stable order
        combined.sort((a, b) => a.Title.localeCompare(b.Title));

        setAllItems(combined);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error("Error fetching Test Cases:", e);
      } finally {
        setLoading(false);
      }
    };

    void fetchDocuments();
  }, []);

  // Limit this section to 8 items max; carousel pages show 4 at a time
  const limited = allItems.slice(0, Math.min(allItems.length, maxInitialItems));
  const visibleItems = limited.slice(startIndex, startIndex + visibleCount);
  const pageCount = Math.ceil((limited.length || 1) / visibleCount);
  const activePage = Math.floor(startIndex / visibleCount);

  const goToNext = (): void => {
    setStartIndex((prev) =>
      prev + visibleCount >= limited.length ? 0 : prev + visibleCount
    );
  };

  const goToPrevious = (): void => {
    setStartIndex((prev) =>
      prev - visibleCount < 0 ? Math.max(limited.length - visibleCount, 0) : prev - visibleCount
    );
  };

  return (
    <div className={`section-common sectionHandbooks`}>
      {/* Header + Nav */}
      <div className={`${styles.carouselHeader} section-heading`}>
        <h2>Test Cases</h2>
        <div className={styles.navButtons}>
          <button onClick={goToPrevious} aria-label="Previous">&#9664;</button>
          <button onClick={goToNext} aria-label="Next">&#9654;</button>
        </div>
      </div>

      {/* Grid */}
      <div className={`${styles.videoCarousel} sectionHandbookPrimary`}>
        <div className={`${styles.carouselContainer} sectionHandbookContainer`}>
          <div className={`${styles.videoGrid} sectionHandbookCarousel`}>
            {(loading ? [] : visibleItems).map((item, index) => (
              <div className={styles.card} key={`${item.Title}-${index}`}>
                <div className="sectionHandbookCardContent">
                  <div className="contentImageWrapper">
                    <a
                      href={`${item.Videos}?web=1`}
                      target="_blank"
                      rel="noopener noreferrer"
                      title={item.Title}
                    >
                      {isVideo(item.Videos) ? (
                        <video
                          controls
                          className={styles.previewImage}
                          poster={item.PreviewUrl || "/assets/video-placeholder.png"}
                        >
                          <source src={item.Videos} type="video/mp4" />
                        </video>
                      ) : (
                        <img
                          src={item.PreviewUrl || defaultDocImage}
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = defaultDocImage;
                          }}
                          alt={`Preview of ${item.Title}`}
                          className={styles.previewImage}
                        />
                      )}
                    </a>
                  </div>
                  <div className="section-handbook-text-info">
                    <h3>{item.Title}</h3>
                  </div>
                </div>
              </div>
            ))}

            {!loading && limited.length === 0 && (
              <div style={{ padding: "1rem" }}>No items found.</div>
            )}
          </div>

          {/* Pagination dots */}
          <div className={styles.paginationDots}>
            {Array.from({ length: pageCount }).map((_, idx) => (
              <button
                key={idx}
                className={`${styles.dot} ${idx === activePage ? styles.activeDot : ""}`}
                onClick={() => setStartIndex(idx * visibleCount)}
                aria-label={`Go to page ${idx + 1}`}
              />
            ))}
          </div>
        </div>

        {/* Read More */}
        <div className={styles.readMoreContainer}>
          <button
            onClick={() => onReadMore(allItems)}  // pass ALL fetched items to AllVideosView
            className={styles.readMoreButton}
            disabled={loading || allItems.length === 0}
          >
            Read More
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestCases;
