import * as React from "react";
import styles from "../components/AllVideosView.module.scss";
import { useLocale } from "../../../common/useLocale";
import PlatonLogo from "../assets/PLATON_Logo.svg";

interface DocumentItem {
  Title: string;
  Videos: string;
  PreviewUrl?: string;
  Thumbnail?: string;
}

interface AllVideosViewProps {
  title: string;           // Display title passed from parent (parent can update this on lang change)
  docData: DocumentItem[];
  onBack: () => void;
}

const BATCH_SIZE = 20;

const AllVideosView: React.FC<AllVideosViewProps> = ({
  title,
  docData,
  onBack,
}) => {
  const { strings } = useLocale(); // 👈 live strings when language changes
  const [searchText, setSearchText] = React.useState<string>("");
  const [visibleCount, setVisibleCount] = React.useState<number>(BATCH_SIZE);

  // Always land at top when opening or when title changes (e.g., language-switch updated title)
  React.useLayoutEffect(() => {
    window.scrollTo({ top: 0, left: 0, behavior: "auto" });
  }, [title]);

  // Memoized filtering for perf on large lists
  const filteredVideos = React.useMemo(() => {
    const q = searchText.trim().toLowerCase();
    if (!q) return docData;
    return docData.filter((item) => item.Title?.toLowerCase().includes(q));
  }, [docData, searchText]);

  const videosToShow = React.useMemo(
    () => filteredVideos.slice(0, visibleCount),
    [filteredVideos, visibleCount]
  );

  const loadMore = (): void => setVisibleCount((prev) => prev + BATCH_SIZE);

  // Helper: case-insensitive video extension check
  const isVideo = (url: string): boolean => {
    const u = (url || "").toLowerCase();
    return u.endsWith(".mp4") || u.endsWith(".mov") || u.endsWith(".webm") || u.endsWith(".m4v");
  };

  // Helper: WMV fallback (will still try mp4 source so it plays in most browsers if transcoded)
  const isWMV = (url: string): boolean => (url || "").toLowerCase().endsWith(".wmv");

  // Localized/fallback strings
  const searchPlaceholder =
    ((strings as unknown as { Common?: { SearchPlaceholder?: string } }).Common?.SearchPlaceholder) ?? "Search videos...";
  const showingStats = `Showing ${Math.min(
    visibleCount,
    filteredVideos.length
  )} of ${filteredVideos.length} records`;

  return (
    <div className={styles.allVideosContainer}>
      {/* Header Section */}
      <div className={styles.customHeader}>
        <div>
          <h2 className={styles.pageTitle}>{title}</h2>
          <p className={styles.pageSubtitle}>{strings.Common.AllVideoText}</p>
          <div className={styles.totalCount}>
            ▶️ {docData.length} {strings.Common.TotalVideo}
          </div>
        </div>
        <button className={styles.backButton} onClick={onBack} aria-label={strings.Common.Back}>
          {strings.Common.Back}
        </button>
      </div>

      {/* Search Section */}
      <div>
        <div className={styles.searchSection}>
          <div className={styles.stats}>{showingStats}</div>
          <div className={styles.searchWrapper}>
            <span className={styles.searchIcon} aria-hidden="true">
              <i className="fas fa-search" />
            </span>
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className={styles.searchInput}
              aria-label={searchPlaceholder}
            />
            {searchText && (
              <button
                className={styles.clearIcon}
                onClick={() => setSearchText("")}
                title="Clear"
                aria-label="Clear search"
              >
                <i className="fas fa-times" />
              </button>
            )}
          </div>
        </div>
        <hr />
      </div>

      {/* Video Grid */}
      <div className={styles.videoGrid}>
        {videosToShow.length > 0 ? (
          videosToShow.map((item, idx) => {
            const key = `${item.Videos}-${idx}`;
            const lower = (item.Videos || "").toLowerCase();

            return (
              <a
                key={key}
                href={`${item.Videos}?web=1`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className={styles.card}>
                  {isVideo(lower) || isWMV(lower) ? (
                    <video
                      controls
                      width="100%"
                      className={styles.previewVideo}
                      poster={item.Thumbnail || PlatonLogo}
                      preload="metadata"
                    >
                      {/* Using mp4 source increases chance of playback support */}
                      <source src={item.Videos} type="video/mp4" />
                    </video>
                  ) : (
                    <img
                      src={item.PreviewUrl || PlatonLogo}
                      alt={`Preview of ${item.Title}`}
                      className={styles.previewImage}
                      loading="lazy"
                    />
                  )}
                  <div className={styles.title}>{item.Title}</div>
                </div>
              </a>
            );
          })
        ) : (
          <div className={styles.noResults}>{strings.Common.NoRecordText}</div>
        )}
      </div>

      {/* Load More Button */}
      {visibleCount < filteredVideos.length && (
        <div className={styles.loadMoreContainer}>
          <button
            className={styles.loadMoreButton}
            onClick={loadMore}
            aria-label={strings.Common.LoadMore}
          >
            {strings.Common.LoadMore}
          </button>
        </div>
      )}
    </div>
  );
};

export default AllVideosView;
