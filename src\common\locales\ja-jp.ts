const jpStrings = {
    Common: {
      ReadMore: "さらに",
      Previous: "前",
      Next: "次",
      Back: "バック",
      GoToPage: "Go to page {page}",
      PreviewAlt: "Preview of {title}",
      PDFAlt: "PDF thumbnail",
      PPTAlt: "PPT thumbnail",
      DurationLabel: "期間",
      AllVideoText: "すべてのビデオチュートリアルとガイドを見る",
      LoadMore: "もっと見る",
      NoRecordText: "記録は見つかりませんでした。",
      TotalVideo: "合計ビデオ",
      KeyUserTitle: "キーユーザー情報",
      KeyUserSubtitle: "すべてのビデオチュートリアルとガイドを見る",
    },
    HeaderCarousel: {
      SectionTitle: "人気のカテゴリーから学ぶ",
      PLATONShortTrips: "PLATONへショート旅行",
      PLATONDepthSessions: "PLATON デプスセッション",
      PLATONModuleSeries: "PLATON モジュールシリーズ",
      PLATONAccessPlaton: "PLATONをアクセス",
      PLATONManuals: "PLATON マニュアル",
      PLATONManualsJapanese: "PLATO<PERSON> 日本語マニュアル",
      PLATONTestCasesDemos: "PLATON テストケースデーモ",
      PLATONTestCasesGuide: "PLATON テストケースガイド",
      PLATONTestCases: "PLATON テストケース",
      Cards: {
        Home: "ホームページ",
        StartingPoint: "スタートポイント",
        ShortTrips: "PLATONへショート旅行",
        DepthSessions: "PLATON デプスセッション",
        ModuleSeries: "PLATON モジュールシリーズ",
        keyUserInfo: "キーユーザー情報",
        Manuals: "PLATON マニュアル",
        BlockTraining: "トレーニングを予約",
        AccessPlaton: "PLATONをアクセス",
        PeakCapture: "ハイライト",
        ProjectEngland: "プロジェクトEngland",
        ContactUs: "お問い合わせください",
        TestCase: "PLATON Test Cases",
      },
    }
  };
  
  export default jpStrings;
  